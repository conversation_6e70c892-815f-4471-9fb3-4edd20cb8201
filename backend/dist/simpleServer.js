"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const path_1 = __importDefault(require("path"));
const topicReader_1 = require("./topicReader");
const app = (0, express_1.default)();
const PORT = 3001;
// Middleware
app.use((0, cors_1.default)());
app.use(express_1.default.json());
// ION file path
const ION_FILE_PATH = path_1.default.join(__dirname, '../../OhmniClean_log.ion');
// Health check
app.get('/health', (req, res) => {
    res.json({ status: 'ok', timestamp: new Date().toISOString() });
});
// Get readable topics
app.get('/api/topics/readable', async (req, res) => {
    try {
        const topics = await (0, topicReader_1.getReadableTopics)(ION_FILE_PATH);
        res.json(topics);
    }
    catch (error) {
        console.error('Error:', error);
        res.status(500).json({ error: 'Failed to get topics' });
    }
});
// Get playback time range
app.get('/api/playback/timerange', async (req, res) => {
    try {
        const timeRange = await (0, topicReader_1.getPlaybackTimeRange)(ION_FILE_PATH);
        res.json(timeRange);
    }
    catch (error) {
        console.error('Error:', error);
        res.status(500).json({ error: 'Failed to get time range' });
    }
});
// Get message at timestamp
app.get('/api/topics/:topicName/message', async (req, res) => {
    try {
        const { topicName } = req.params;
        const timestamp = parseFloat(req.query.timestamp);
        if (isNaN(timestamp)) {
            return res.status(400).json({ error: 'Invalid timestamp' });
        }
        const message = await (0, topicReader_1.getMessageAtTimestamp)(ION_FILE_PATH, topicName, timestamp);
        if (!message) {
            return res.status(404).json({ error: 'No message found' });
        }
        res.json(message);
    }
    catch (error) {
        console.error('Error:', error);
        res.status(500).json({ error: 'Failed to get message' });
    }
});
// Start server
app.listen(PORT, () => {
    console.log(`🚀 ION Playback Server running on http://localhost:${PORT}`);
    console.log(`📁 Using ION file: ${ION_FILE_PATH}`);
    console.log(`🌐 API endpoints:`);
    console.log(`   GET /health`);
    console.log(`   GET /api/topics/readable`);
    console.log(`   GET /api/playback/timerange`);
    console.log(`   GET /api/topics/:topicName/message?timestamp=123`);
});
exports.default = app;
//# sourceMappingURL=simpleServer.js.map