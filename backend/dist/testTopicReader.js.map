{"version": 3, "file": "testTopicReader.js", "sourceRoot": "", "sources": ["../src/testTopicReader.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2HS,0CAAe;AA3HxB,+CAKuB;AACvB,2CAA6B;AAE7B;;GAEG;AACH,KAAK,UAAU,eAAe;IAC5B,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC;IACjD,OAAO,CAAC,GAAG,CAAC,qCAAqC,CAAC,CAAC;IAEnD,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,0BAA0B,CAAC,CAAC;IAElE,IAAI,CAAC;QACH,8BAA8B;QAC9B,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC;QAC9C,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC;QAC9C,MAAM,MAAM,GAAG,MAAM,IAAA,+BAAiB,EAAC,QAAQ,CAAC,CAAC;QACjD,OAAO,CAAC,GAAG,CAAC,SAAS,MAAM,CAAC,MAAM,mBAAmB,CAAC,CAAC;QAEvD,sBAAsB;QACtB,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE;YAC1C,OAAO,CAAC,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;YAC3C,OAAO,CAAC,GAAG,CAAC,YAAY,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;YACtC,OAAO,CAAC,GAAG,CAAC,gBAAgB,KAAK,CAAC,YAAY,EAAE,CAAC,CAAC;YAClD,OAAO,CAAC,GAAG,CAAC,iBAAiB,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;YAC9D,OAAO,CAAC,GAAG,CAAC,kBAAkB,KAAK,CAAC,cAAc,MAAM,KAAK,CAAC,aAAa,EAAE,CAAC,CAAC;YAC/E,OAAO,CAAC,GAAG,EAAE,CAAC;QAChB,CAAC,CAAC,CAAC;QAEH,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACtB,OAAO,CAAC,GAAG,CAAC,WAAW,MAAM,CAAC,MAAM,GAAG,CAAC,gBAAgB,CAAC,CAAC;QAC5D,CAAC;QAED,kCAAkC;QAClC,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;QAClD,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;QAClD,MAAM,SAAS,GAAG,MAAM,IAAA,kCAAoB,EAAC,QAAQ,CAAC,CAAC;QACvD,OAAO,CAAC,GAAG,CAAC,eAAe,SAAS,CAAC,KAAK,EAAE,CAAC,CAAC;QAC9C,OAAO,CAAC,GAAG,CAAC,aAAa,SAAS,CAAC,GAAG,EAAE,CAAC,CAAC;QAC1C,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,SAAS,CAAC,QAAQ,GAAG,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC;QAC3E,OAAO,CAAC,GAAG,EAAE,CAAC;QAEd,mCAAmC;QACnC,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACtB,OAAO,CAAC,GAAG,CAAC,qCAAqC,CAAC,CAAC;YACnD,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAC;YACpD,MAAM,SAAS,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,kBAAkB;YAC/C,MAAM,YAAY,GAAG,CAAC,SAAS,CAAC,cAAc,GAAG,SAAS,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;YAE9E,OAAO,CAAC,GAAG,CAAC,kBAAkB,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC;YAChD,OAAO,CAAC,GAAG,CAAC,uCAAuC,YAAY,EAAE,CAAC,CAAC;YAEnE,MAAM,OAAO,GAAG,MAAM,IAAA,mCAAqB,EAAC,QAAQ,EAAE,SAAS,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;YACpF,IAAI,OAAO,EAAE,CAAC;gBACZ,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;gBAChC,OAAO,CAAC,GAAG,CAAC,iBAAiB,OAAO,CAAC,SAAS,EAAE,CAAC,CAAC;gBAClD,OAAO,CAAC,GAAG,CAAC,iBAAiB,OAAO,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC;gBACpD,OAAO,CAAC,GAAG,CAAC,oBAAoB,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC;gBACrF,IAAI,OAAO,CAAC,cAAc,EAAE,CAAC;oBAC3B,OAAO,CAAC,GAAG,CAAC,gBAAgB,OAAO,CAAC,cAAc,EAAE,CAAC,CAAC;gBACxD,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC;YACpC,CAAC;YACD,OAAO,CAAC,GAAG,EAAE,CAAC;YAEd,uCAAuC;YACvC,OAAO,CAAC,GAAG,CAAC,yCAAyC,CAAC,CAAC;YACvD,OAAO,CAAC,GAAG,CAAC,yCAAyC,CAAC,CAAC;YACvD,MAAM,QAAQ,GAAG,MAAM,IAAA,8BAAgB,EAAC,QAAQ,EAAE,SAAS,CAAC,IAAI,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;YACxF,OAAO,CAAC,GAAG,CAAC,sBAAsB,SAAS,CAAC,IAAI,KAAK,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC;YAExE,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACxB,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAC;gBACjC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE;oBAC1C,OAAO,CAAC,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,gBAAgB,GAAG,CAAC,SAAS,EAAE,CAAC,CAAC;oBACzD,OAAO,CAAC,GAAG,CAAC,YAAY,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC;gBAC3E,CAAC,CAAC,CAAC;YACL,CAAC;YACD,OAAO,CAAC,GAAG,EAAE,CAAC;QAChB,CAAC;QAED,2CAA2C;QAC3C,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAC;QACpD,OAAO,CAAC,GAAG,CAAC,uCAAuC,CAAC,CAAC;QACrD,MAAM,aAAa,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,GAAG,EAAE,CAAC,CAAC;QACzD,IAAI,aAAa,EAAE,CAAC;YAClB,OAAO,CAAC,GAAG,CAAC,iCAAiC,aAAa,CAAC,IAAI,KAAK,aAAa,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;YAE9G,sCAAsC;YACtC,MAAM,WAAW,GAAG,aAAa,CAAC,cAAc,CAAC;YACjD,MAAM,SAAS,GAAG,WAAW,GAAG,IAAI,CAAC,CAAC,kBAAkB;YAExD,MAAM,cAAc,GAAG,MAAM,IAAA,8BAAgB,EAAC,QAAQ,EAAE,aAAa,CAAC,IAAI,EAAE,WAAW,EAAE,SAAS,CAAC,CAAC;YACpG,OAAO,CAAC,GAAG,CAAC,gCAAgC,cAAc,CAAC,MAAM,EAAE,CAAC,CAAC;YACrE,OAAO,CAAC,GAAG,CAAC,wBAAwB,cAAc,CAAC,MAAM,KAAK,CAAC,CAAC;YAEhE,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC9B,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;gBAC/B,OAAO,CAAC,GAAG,CAAC,gBAAgB,cAAc,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC;gBAC3D,OAAO,CAAC,GAAG,CAAC,WAAW,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC;YACxF,CAAC;QACH,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC;QAChD,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,uCAAuC,CAAC,CAAC;IAEvD,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,gBAAgB,EAAE,KAAK,CAAC,CAAC;IACzC,CAAC;AACH,CAAC;AAED,eAAe;AACf,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;IAC5B,eAAe,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AACzC,CAAC"}