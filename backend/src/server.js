const express = require('express');
const cors = require('cors');
const path = require('path');

// Import the compiled JavaScript versions of our functions
// We'll need to compile TypeScript first
const app = express();
const PORT = 3001;

// Middleware
app.use(cors());
app.use(express.json());

// ION file path
const ION_FILE_PATH = path.join(__dirname, '../../OhmniClean_log.ion');

// For now, let's create a simple mock server to test the frontend
// We'll replace this with actual functions once TypeScript compilation works

// Import the ION parser and topic reader to access real ION data
const { parseIonMetadata } = require('../dist/ionParser');
const { getMessageAtTimestamp } = require('../dist/topicReader');

// Cache for real topics data
let cachedTopics = null;
// Cache for parsed ION data to avoid re-parsing on every request
let cachedIonData = null;
let isIonDataLoading = false;
// Cache for pre-sorted topic messages for ultra-fast lookup
let cachedTopicMessages = new Map();

/**
 * Preload and cache ION data at server startup
 */
async function preloadIonData() {
  if (cachedIonData || isIonDataLoading) {
    return cachedIonData;
  }

  try {
    isIonDataLoading = true;
    console.log('🚀 Preloading ION data at server startup...');
    const startTime = Date.now();

    const metadata = await parseIonMetadata(ION_FILE_PATH);
    cachedIonData = metadata.rawData;

    // Pre-sort and cache messages for each topic for ultra-fast lookup
    console.log('🔄 Pre-sorting messages for fast lookup...');
    const sortStartTime = Date.now();

    if (cachedIonData?.topics && Array.isArray(cachedIonData.topics)) {
      cachedIonData.topics.forEach(topic => {
        if (topic.topicName && topic.messages && Array.isArray(topic.messages)) {
          // Filter and sort messages once during startup
          const sortedMessages = topic.messages
            .filter(msg => msg && msg.data && typeof msg.timestamp === 'number')
            .sort((a, b) => a.timestamp - b.timestamp);

          // Cache the sorted messages
          cachedTopicMessages.set(topic.topicName, sortedMessages);
          console.log(`📋 Cached ${sortedMessages.length} messages for topic: ${topic.topicName}`);
        }
      });
    }

    const sortTime = Date.now() - sortStartTime;
    const totalTime = Date.now() - startTime;
    console.log(`✅ Message sorting completed in ${sortTime}ms`);
    console.log(`✅ ION data preloaded successfully in ${totalTime}ms`);
    console.log(`📊 Found ${cachedIonData?.topics?.length || 0} topics in ION file`);
    console.log(`🚀 Cached ${cachedTopicMessages.size} topics for ultra-fast message lookup`);

    return cachedIonData;
  } catch (error) {
    console.error('❌ Error preloading ION data:', error);
    return null;
  } finally {
    isIonDataLoading = false;
  }
}

/**
 * Ultra-fast message retrieval using pre-cached sorted messages
 */
async function getMessageAtTimestampFast(topicName, timestamp) {
  // Ensure ION data is loaded
  if (!cachedIonData) {
    await preloadIonData();
  }

  // Get pre-sorted messages from cache
  const sortedMessages = cachedTopicMessages.get(topicName);
  if (!sortedMessages || sortedMessages.length === 0) {
    return null;
  }

  // Find nearest timestamp using binary search for efficiency
  let left = 0;
  let right = sortedMessages.length - 1;
  let bestMatch = sortedMessages[0];
  let minDiff = Math.abs(sortedMessages[0].timestamp - timestamp);

  while (left <= right) {
    const mid = Math.floor((left + right) / 2);
    const message = sortedMessages[mid];
    const diff = Math.abs(message.timestamp - timestamp);

    if (diff < minDiff) {
      minDiff = diff;
      bestMatch = message;
    }

    if (message.timestamp < timestamp) {
      left = mid + 1;
    } else if (message.timestamp > timestamp) {
      right = mid - 1;
    } else {
      // Exact match
      bestMatch = message;
      break;
    }
  }

  return {
    timestamp: bestMatch.timestamp,
    data: bestMatch.data,
    sequenceNumber: bestMatch.data?.header?.seq
  };
}

async function loadTopicsFromIon() {
  if (cachedTopics) {
    return cachedTopics;
  }

  try {
    console.log('📡 Loading real topics from ION file...');

    // Ensure ION data is preloaded
    if (!cachedIonData) {
      await preloadIonData();
    }

    if (!cachedIonData?.topics || !Array.isArray(cachedIonData.topics)) {
      console.warn('No topics found in ION file');
      return [];
    }

    // Convert ION topics to our format, filtering for readable topics
    cachedTopics = cachedIonData.topics
      .filter(topic => topic.topicName && topic.messages && Array.isArray(topic.messages) && topic.messages.length > 0)
      .map(topic => {
        // Get timestamps for time range
        const timestamps = topic.messages
          .map(msg => msg.timestamp)
          .filter(ts => typeof ts === 'number')
          .sort((a, b) => a - b);

        return {
          name: topic.topicName,
          type: topic.topicType || 'unknown',
          frequency: topic.frequency || 0,
          messageCount: topic.messages.length,
          firstTimestamp: timestamps[0] || 0,
          lastTimestamp: timestamps[timestamps.length - 1] || 0
        };
      })
      .filter(topic => topic.messageCount > 0); // Only include topics with messages

    console.log(`📡 Successfully loaded ${cachedTopics.length} real topics from ION file`);
    return cachedTopics;

  } catch (error) {
    console.error('Error loading topics from ION file:', error);
    console.log('📡 Falling back to empty topics array');
    return [];
  }
}

// Cache for time range data
let cachedTimeRange = null;

async function loadTimeRangeFromIon() {
  if (cachedTimeRange) {
    return cachedTimeRange;
  }

  try {
    console.log('⏱️ Loading time range from cached ION data...');

    // Ensure ION data is preloaded
    if (!cachedIonData) {
      await preloadIonData();
    }

    // Get time range from all cached topic messages
    let minTimestamp = Infinity;
    let maxTimestamp = -Infinity;
    let messageCount = 0;

    cachedTopicMessages.forEach((messages, topicName) => {
      if (messages.length > 0) {
        // Filter out messages with invalid timestamps (0 or negative)
        const validMessages = messages.filter(msg => msg.timestamp > 1000000000000); // Unix timestamp in milliseconds

        if (validMessages.length > 0) {
          const firstMsg = validMessages[0];
          const lastMsg = validMessages[validMessages.length - 1];

          if (firstMsg.timestamp < minTimestamp) {
            minTimestamp = firstMsg.timestamp;
          }
          if (lastMsg.timestamp > maxTimestamp) {
            maxTimestamp = lastMsg.timestamp;
          }
        }
        messageCount += messages.length;
      }
    });

    if (minTimestamp === Infinity || maxTimestamp === -Infinity) {
      console.warn('No valid timestamps found in cached messages');
      return { start: 0, end: 0, duration: 0 };
    }

    cachedTimeRange = {
      start: minTimestamp,
      end: maxTimestamp,
      duration: maxTimestamp - minTimestamp
    };

    console.log(`⏱️ Time range: ${cachedTimeRange.start} to ${cachedTimeRange.end} (${cachedTimeRange.duration}ms)`);
    console.log(`⏱️ Total messages across all topics: ${messageCount}`);
    return cachedTimeRange;

  } catch (error) {
    console.error('Error loading time range from cached data:', error);
    return { start: 0, end: 0, duration: 0 };
  }
}

// Health check
app.get('/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

// Get readable topics
app.get('/api/topics/readable', async (req, res) => {
  console.log('📡 GET /api/topics/readable');
  try {
    const topics = await loadTopicsFromIon();
    res.json(topics);
  } catch (error) {
    console.error('Error getting topics:', error);
    res.status(500).json({ error: 'Failed to get topics' });
  }
});

// Get playback time range
app.get('/api/playback/timerange', async (req, res) => {
  console.log('⏱️  GET /api/playback/timerange');
  try {
    const timeRange = await loadTimeRangeFromIon();
    res.json(timeRange);
  } catch (error) {
    console.error('Error getting time range:', error);
    res.status(500).json({ error: 'Failed to get time range' });
  }
});

// Get message at timestamp
app.get('/api/topics/:topicName/message', async (req, res) => {
  try {
    const topicName = decodeURIComponent(req.params.topicName);
    const timestamp = parseFloat(req.query.timestamp);

    console.log(`📨 GET /api/topics/${topicName}/message?timestamp=${timestamp}`);

    if (isNaN(timestamp)) {
      return res.status(400).json({ error: 'Invalid timestamp' });
    }

    // Get real message from cached ION data (much faster)
    const message = await getMessageAtTimestampFast(topicName, timestamp);

    if (!message) {
      return res.status(404).json({ error: 'No message found for the given timestamp' });
    }

    res.json(message);
  } catch (error) {
    console.error('Error getting message:', error);
    res.status(500).json({ error: 'Failed to get message' });
  }
});

// Get ION metadata
app.get('/api/metadata', (req, res) => {
  console.log('📊 GET /api/metadata');

  try {
    // Mock metadata based on OhmniClean_log.ion analysis
    const metadata = {
      session: {
        sessionId: 'ohmni_clean_session_001',
        timestamp: '2024-01-15T14:30:00Z',
        startTime: '2024-01-15T14:30:00Z',
        endTime: '2024-01-15T14:32:21Z',
        duration: 141000, // 141 seconds in milliseconds
        version: '1.0.0',
        sessionCode: 'CLEAN_001',
        operatorId: 'operator_123',
        operatorName: 'John Doe',
        mapId: 'map_office_floor1',
        mapName: 'Office Floor 1',
        result: 'SUCCESS',
        sessionType: 'cleaning'
      },
      robot: {
        robotId: 'ohmni_robot_001',
        robotName: 'OhmniClean-Alpha',
        model: 'OhmniClean-Pro',
        firmware: 'v3.2.1',
        serialNumber: 'OC-2024-001',
        capabilities: ['autonomous_navigation', 'cleaning', 'mapping', 'obstacle_avoidance', 'voice_control'],
        sensors: ['lidar', 'camera', 'imu', 'ultrasonic', 'wheel_encoders', 'cliff_sensors'],
        enterpriseName: 'Ohmnilabs Inc.',
        enterpriseID: 'ohmnilabs_001',
        siteID: 'site_office_001',
        mapID: 'map_office_floor1',
        mapVersionID: 'v1.2.3',
        releaseTrack: 'stable',
        apkVersion: '2.1.4',
        server: 'ohmni-cloud.com',
        autonomyServer: 'autonomy.ohmni-cloud.com'
      }
    };

    res.json(metadata);
  } catch (error) {
    console.error('Error getting metadata:', error);
    res.status(500).json({ error: 'Failed to load metadata' });
  }
});

// Cache for real log data from /rosout_agg topic
let cachedLogs = null;

async function loadLogsFromIon() {
  if (cachedLogs) {
    return cachedLogs;
  }

  try {
    console.log('📜 Loading real log messages from cached /rosout_agg topic...');

    // Ensure ION data is preloaded
    if (!cachedIonData) {
      await preloadIonData();
    }

    // Get pre-cached messages for /rosout_agg topic
    const rosoutMessages = cachedTopicMessages.get('/rosout_agg');

    if (!rosoutMessages || rosoutMessages.length === 0) {
      console.warn('No /rosout_agg topic or messages found in cached data');
      return [];
    }

    // Convert cached messages to our log format
    cachedLogs = rosoutMessages.map(message => ({
      timestamp: message.timestamp,
      level: message.data?.level || 1,
      name: message.data?.name || 'unknown',
      msg: message.data?.msg || '',
      file: message.data?.file || '',
      function: message.data?.function || '',
      line: message.data?.line || 0
    }));

    console.log(`📜 Successfully loaded ${cachedLogs.length} real log messages from cached /rosout_agg topic`);
    return cachedLogs;

  } catch (error) {
    console.error('Error loading logs from cached data:', error);
    console.log('📜 Falling back to empty logs array');
    return [];
  }
}

// Get logs up to timestamp with optional search filter
app.get('/api/logs', async (req, res) => {
  const timestamp = parseFloat(req.query.timestamp);
  const search = req.query.search || '';

  console.log(`📜 GET /api/logs?timestamp=${timestamp}&search=${search}`);

  if (isNaN(timestamp)) {
    return res.status(400).json({ error: 'Invalid timestamp parameter' });
  }

  try {
    // Load real logs from ION file
    const allLogs = await loadLogsFromIon();

    // Filter logs up to the given timestamp
    let filteredLogs = allLogs.filter(log => log.timestamp <= timestamp);

    // Apply search filter if provided
    if (search) {
      const searchLower = search.toLowerCase();
      filteredLogs = filteredLogs.filter(log =>
        log.msg.toLowerCase().includes(searchLower) ||
        log.name.toLowerCase().includes(searchLower)
      );
    }

    // Sort by timestamp (ascending)
    filteredLogs.sort((a, b) => a.timestamp - b.timestamp);

    res.json(filteredLogs);
  } catch (error) {
    console.error('Error getting logs:', error);
    res.status(500).json({ error: 'Failed to get logs' });
  }
});

// Error handling
app.use((err, req, res, next) => {
  console.error('Error:', err);
  res.status(500).json({ error: 'Internal server error' });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({ error: 'Endpoint not found' });
});

// Start server and preload ION data
app.listen(PORT, async () => {
  console.log(`🚀 ION Playback Server running on http://localhost:${PORT}`);
  console.log(`📁 Using ION file: ${ION_FILE_PATH}`);
  console.log(`🌐 API endpoints:`);
  console.log(`   GET /health`);
  console.log(`   GET /api/topics/readable`);
  console.log(`   GET /api/playback/timerange`);
  console.log(`   GET /api/topics/:topicName/message?timestamp=123`);
  console.log(`   GET /api/metadata`);
  console.log(`   GET /api/logs?timestamp=123&search=optional`);
  console.log(`\n✅ Server now loads real data from ION file!`);

  // Preload ION data for fast message streaming
  await preloadIonData();
  console.log(`🚀 Server ready for fast message streaming!`);
});

module.exports = app;
