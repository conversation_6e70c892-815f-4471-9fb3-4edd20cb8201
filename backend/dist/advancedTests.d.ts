/**
 * Advanced testing utilities for ION file analysis
 */
interface TopicInfo {
    topicName: string;
    topicType: string;
    frequency: number;
    messageCount: number;
    firstTimestamp?: number;
    lastTimestamp?: number;
    sampleMessage?: any;
}
interface PerformanceMetrics {
    fileSize: number;
    parseTime: number;
    memoryUsage: {
        before: NodeJS.MemoryUsage;
        after: NodeJS.MemoryUsage;
        delta: NodeJS.MemoryUsage;
    };
}
/**
 * Analyzes topics in the ION file
 */
declare function analyzeTopics(rawData: any): TopicInfo[];
/**
 * Analyzes map data if present
 */
declare function analyzeMapData(rawData: any): any;
/**
 * Performance test for parsing
 */
declare function performanceTest(filePath: string): Promise<PerformanceMetrics>;
/**
 * Comprehensive analysis of the ION file
 */
declare function comprehensiveAnalysis(): Promise<void>;
/**
 * Export data to JSON for further analysis
 */
declare function exportToJson(): Promise<void>;
export { comprehensiveAnalysis, exportToJson, analyzeTopics, analyzeMapData, performanceTest };
//# sourceMappingURL=advancedTests.d.ts.map