/**
 * Interface for session information extracted from ION data
 */
export interface SessionInfo {
    sessionId?: string;
    timestamp?: string;
    duration?: number;
    startTime?: string;
    endTime?: string;
    version?: string;
    sessionCode?: string;
    operatorId?: string;
    operatorName?: string;
    mapId?: string;
    mapName?: string;
    result?: string;
    sessionType?: string;
    [key: string]: any;
}
/**
 * Interface for robot metadata extracted from ION data
 */
export interface RobotInfo {
    robotId?: string;
    robotName?: string;
    model?: string;
    firmware?: string;
    serialNumber?: string;
    capabilities?: string[];
    sensors?: string[];
    enterpriseName?: string;
    enterpriseID?: string;
    siteID?: any;
    mapID?: string;
    mapVersionID?: string;
    releaseTrack?: string;
    apkVersion?: string;
    server?: string;
    autonomyServer?: string;
    configuration?: any;
    [key: string]: any;
}
/**
 * Interface for the complete metadata structure
 */
export interface IonMetadata {
    session: SessionInfo;
    robot: RobotInfo;
    rawData?: any;
}
/**
 * Main function to parse ION file and extract metadata
 * @param filePath - Path to the binary ION file
 * @returns Promise<IonMetadata> - Structured metadata containing session and robot info
 */
export declare function parseIonMetadata(filePath: string): Promise<IonMetadata>;
/**
 * Utility function to validate ION file format
 */
export declare function isValidIonFile(filePath: string): boolean;
//# sourceMappingURL=ionParser.d.ts.map