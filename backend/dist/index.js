"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.isValidIonFile = exports.parseIonMetadata = void 0;
const ionParser_1 = require("./ionParser");
const path = __importStar(require("path"));
/**
 * Example usage of the ION parser
 * This demonstrates how to use the parseIonMetadata function
 */
async function main() {
    console.log('ION Playback Backend - Task 1: Parse ION and Display Metadata');
    console.log('================================================================');
    // Test with the OhmniClean_log.ion file
    const exampleFilePath = path.join(__dirname, '../../OhmniClean_log.ion');
    try {
        // Check if file exists and is valid ION
        if ((0, ionParser_1.isValidIonFile)(exampleFilePath)) {
            console.log(`Parsing ION file: ${exampleFilePath}`);
            const metadata = await (0, ionParser_1.parseIonMetadata)(exampleFilePath);
            console.log('\n📊 Extracted Metadata:');
            console.log('======================');
            console.log('\n🔧 Session Information:');
            console.log(JSON.stringify(metadata.session, null, 2));
            console.log('\n🤖 Robot Information:');
            console.log(JSON.stringify(metadata.robot, null, 2));
        }
        else {
            console.log(`Sample ION file not found at: ${exampleFilePath}`);
            console.log('To test the parser, place a binary ION file at the above path.');
        }
    }
    catch (error) {
        console.error('Error parsing ION file:', error);
    }
}
// Export the parser functions for use by other modules
var ionParser_2 = require("./ionParser");
Object.defineProperty(exports, "parseIonMetadata", { enumerable: true, get: function () { return ionParser_2.parseIonMetadata; } });
Object.defineProperty(exports, "isValidIonFile", { enumerable: true, get: function () { return ionParser_2.isValidIonFile; } });
// Run main function if this file is executed directly
if (require.main === module) {
    main().catch(console.error);
}
//# sourceMappingURL=index.js.map