"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.getIonFileInfo = getIonFileInfo;
exports.extractTopics = extractTopics;
exports.getSessionSummary = getSessionSummary;
const ionParser_1 = require("./ionParser");
const fs = __importStar(require("fs"));
/**
 * Utility functions for working with ION files
 */
/**
 * Quick info about an ION file
 */
async function getIonFileInfo(filePath) {
    if (!fs.existsSync(filePath)) {
        throw new Error(`File not found: ${filePath}`);
    }
    const stats = fs.statSync(filePath);
    const isValid = (0, ionParser_1.isValidIonFile)(filePath);
    const info = {
        path: filePath,
        size: `${(stats.size / 1024 / 1024).toFixed(2)} MB`,
        modified: stats.mtime.toISOString(),
        isValidIon: isValid
    };
    if (isValid) {
        try {
            const startTime = Date.now();
            const metadata = await (0, ionParser_1.parseIonMetadata)(filePath);
            const parseTime = Date.now() - startTime;
            return {
                ...info,
                parseTime: `${parseTime} ms`,
                sessionId: metadata.session.sessionId,
                robotName: metadata.robot.robotName,
                topicCount: Array.isArray(metadata.rawData?.topics) ? metadata.rawData.topics.length : 0
            };
        }
        catch (error) {
            return {
                ...info,
                parseError: error instanceof Error ? error.message : String(error)
            };
        }
    }
    return info;
}
/**
 * Extract specific topics from ION file
 */
async function extractTopics(filePath, topicNames) {
    const metadata = await (0, ionParser_1.parseIonMetadata)(filePath);
    if (!metadata.rawData?.topics || !Array.isArray(metadata.rawData.topics)) {
        return [];
    }
    let topics = metadata.rawData.topics;
    // Filter by topic names if specified
    if (topicNames && topicNames.length > 0) {
        topics = topics.filter((topic) => topicNames.includes(topic.topicName));
    }
    return topics.map((topic) => ({
        name: topic.topicName,
        type: topic.topicType,
        frequency: topic.frequency,
        messageCount: topic.messages ? topic.messages.length : 0,
        messages: topic.messages || []
    }));
}
/**
 * Get session summary
 */
async function getSessionSummary(filePath) {
    const metadata = await (0, ionParser_1.parseIonMetadata)(filePath);
    return {
        session: {
            id: metadata.session.sessionId,
            code: metadata.session.sessionCode,
            type: metadata.session.sessionType,
            duration: metadata.session.duration,
            startTime: metadata.session.startTime,
            endTime: metadata.session.endTime,
            result: metadata.session.result,
            completeness: metadata.session.completeness,
            operator: metadata.session.operatorName,
            mapName: metadata.session.mapName
        },
        robot: {
            id: metadata.robot.robotId,
            name: metadata.robot.robotName,
            enterprise: metadata.robot.enterpriseName,
            firmware: metadata.robot.firmware,
            lidarModel: metadata.robot.configuration?.LIDAR_MODEL
        }
    };
}
/**
 * Command line interface for ION utilities
 */
async function cli() {
    const args = process.argv.slice(2);
    if (args.length === 0) {
        console.log('ION Utilities - Command Line Interface');
        console.log('=====================================');
        console.log('');
        console.log('Usage:');
        console.log('  npx ts-node src/ionUtils.ts <command> <file> [options]');
        console.log('');
        console.log('Commands:');
        console.log('  info <file>              - Get basic file information');
        console.log('  summary <file>           - Get session summary');
        console.log('  topics <file> [names]    - List topics (optionally filter by names)');
        console.log('');
        console.log('Examples:');
        console.log('  npx ts-node src/ionUtils.ts info ../../OhmniClean_log.ion');
        console.log('  npx ts-node src/ionUtils.ts summary ../../OhmniClean_log.ion');
        console.log('  npx ts-node src/ionUtils.ts topics ../../OhmniClean_log.ion /tf /tb_cmd_vel');
        return;
    }
    const command = args[0];
    const filePath = args[1];
    if (!filePath) {
        console.error('Error: File path is required');
        return;
    }
    try {
        switch (command) {
            case 'info':
                const info = await getIonFileInfo(filePath);
                console.log('📄 ION File Information:');
                console.log('========================');
                Object.entries(info).forEach(([key, value]) => {
                    console.log(`${key}: ${value}`);
                });
                break;
            case 'summary':
                const summary = await getSessionSummary(filePath);
                console.log('📊 Session Summary:');
                console.log('==================');
                console.log('\n🔧 Session:');
                Object.entries(summary.session).forEach(([key, value]) => {
                    console.log(`  ${key}: ${value}`);
                });
                console.log('\n🤖 Robot:');
                Object.entries(summary.robot).forEach(([key, value]) => {
                    console.log(`  ${key}: ${value}`);
                });
                break;
            case 'topics':
                const topicNames = args.slice(2);
                const topics = await extractTopics(filePath, topicNames.length > 0 ? topicNames : undefined);
                console.log('📡 Topics:');
                console.log('=========');
                if (topicNames.length > 0) {
                    console.log(`Filtered by: ${topicNames.join(', ')}`);
                }
                console.log(`Total topics: ${topics.length}\n`);
                topics.forEach((topic, index) => {
                    console.log(`${index + 1}. ${topic.name}`);
                    console.log(`   Type: ${topic.type}`);
                    console.log(`   Frequency: ${topic.frequency} Hz`);
                    console.log(`   Messages: ${topic.messageCount}`);
                    console.log();
                });
                break;
            default:
                console.error(`Unknown command: ${command}`);
                console.log('Use "npx ts-node src/ionUtils.ts" to see available commands');
        }
    }
    catch (error) {
        console.error('Error:', error instanceof Error ? error.message : String(error));
    }
}
// Run CLI if this file is executed directly
if (require.main === module) {
    cli().catch(console.error);
}
//# sourceMappingURL=ionUtils.js.map