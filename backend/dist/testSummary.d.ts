/**
 * Test Summary and Results for OhmniClean_log.ion
 *
 * This file documents the comprehensive testing performed on the ION parser
 * with the OhmniClean_log.ion file.
 */
export interface TestResults {
    fileValidation: {
        exists: boolean;
        size: string;
        isValidIon: boolean;
    };
    performance: {
        parseTime: string;
        memoryUsage: string;
    };
    dataStructure: {
        topLevelKeys: string[];
        totalTopics: number;
        totalMessages: number;
    };
    sessionData: {
        sessionId: string;
        sessionCode: string;
        duration: string;
        sessionType: string;
        result: string;
        completeness: string;
        operator: string;
        mapName: string;
    };
    robotData: {
        robotId: string;
        robotName: string;
        enterprise: string;
        firmware: string;
        lidarModel: string;
        configurationKeys: number;
    };
    topicsAnalysis: {
        mostActiveTopics: Array<{
            name: string;
            type: string;
            messageCount: number;
            frequency: string;
        }>;
    };
    mapData: {
        resolution: string;
        dimensions: string;
        dataSize: string;
    };
}
/**
 * Comprehensive test results for OhmniClean_log.ion
 */
export declare const TEST_RESULTS: TestResults;
/**
 * Test conclusions and recommendations
 */
export declare const TEST_CONCLUSIONS: {
    success: boolean;
    keyFindings: string[];
    improvements: string[];
    dataQuality: string[];
};
/**
 * Print a formatted test summary
 */
export declare function printTestSummary(): void;
//# sourceMappingURL=testSummary.d.ts.map