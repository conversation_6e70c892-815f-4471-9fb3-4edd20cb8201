{"version": 3, "file": "ionParser.js", "sourceRoot": "", "sources": ["../src/ionParser.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoPA,4CAoCC;AAKD,wCAcC;AA3SD,4CAA8B;AAC9B,uCAAyB;AAwDzB;;;GAGG;AACH,SAAS,YAAY,CAAC,KAAoB;IACxC,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,SAAS,EAAE,CAAC;QAC1C,OAAO,IAAI,CAAC;IACd,CAAC;IAED,6BAA6B;IAC7B,QAAQ,KAAK,CAAC,OAAO,EAAE,EAAE,CAAC;QACxB,KAAK,GAAG,CAAC,QAAQ,CAAC,IAAI;YACpB,OAAO,IAAI,CAAC;QAEd,KAAK,GAAG,CAAC,QAAQ,CAAC,IAAI;YACpB,OAAO,KAAK,CAAC,YAAY,EAAE,CAAC;QAE9B,KAAK,GAAG,CAAC,QAAQ,CAAC,GAAG;YACnB,OAAO,KAAK,CAAC,WAAW,EAAE,CAAC;QAE7B,KAAK,GAAG,CAAC,QAAQ,CAAC,KAAK;YACrB,OAAO,KAAK,CAAC,WAAW,EAAE,CAAC;QAE7B,KAAK,GAAG,CAAC,QAAQ,CAAC,OAAO;YACvB,OAAO,KAAK,CAAC,WAAW,EAAE,CAAC;QAE7B,KAAK,GAAG,CAAC,QAAQ,CAAC,SAAS;YACzB,OAAO,KAAK,CAAC,cAAc,EAAE,EAAE,QAAQ,EAAE,CAAC;QAE5C,KAAK,GAAG,CAAC,QAAQ,CAAC,MAAM;YACtB,OAAO,KAAK,CAAC,WAAW,EAAE,CAAC;QAE7B,KAAK,GAAG,CAAC,QAAQ,CAAC,MAAM;YACtB,OAAO,KAAK,CAAC,WAAW,EAAE,CAAC;QAE7B,KAAK,GAAG,CAAC,QAAQ,CAAC,IAAI;YACpB,uDAAuD;YACvD,MAAM,SAAS,GAAG,KAAK,CAAC,eAAe,EAAE,CAAC;YAC1C,OAAO,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QAEtE,KAAK,GAAG,CAAC,QAAQ,CAAC,IAAI;YACpB,yBAAyB;YACzB,MAAM,SAAS,GAAG,KAAK,CAAC,eAAe,EAAE,CAAC;YAC1C,OAAO,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QAEpE,KAAK,GAAG,CAAC,QAAQ,CAAC,IAAI;YACpB,oCAAoC;YACpC,MAAM,YAAY,GAAU,EAAE,CAAC;YAC/B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;gBACjD,YAAY,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACvD,CAAC;YACD,OAAO,YAAY,CAAC;QAEtB,KAAK,GAAG,CAAC,QAAQ,CAAC,IAAI;YACpB,uCAAuC;YACvC,MAAM,YAAY,GAAU,EAAE,CAAC;YAC/B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;gBACjD,YAAY,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACvD,CAAC;YACD,OAAO,YAAY,CAAC;QAEtB,KAAK,GAAG,CAAC,QAAQ,CAAC,MAAM;YACtB,oCAAoC;YACpC,MAAM,SAAS,GAAQ,EAAE,CAAC;YAC1B,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC;YAC9B,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;gBAC3B,MAAM,SAAS,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,sCAAsC;gBAClE,MAAM,UAAU,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC1C,IAAI,SAAS,EAAE,CAAC;oBACd,SAAS,CAAC,SAAS,CAAC,GAAG,UAAU,CAAC;gBACpC,CAAC;YACH,CAAC;YACD,OAAO,SAAS,CAAC;QAEnB;YACE,6BAA6B;YAC7B,OAAO,CAAC,IAAI,CAAC,qBAAqB,KAAK,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;YACrD,OAAO,KAAK,CAAC,QAAQ,EAAE,CAAC;IAC5B,CAAC;AACH,CAAC;AAED;;GAEG;AACH,SAAS,kBAAkB,CAAC,IAAS;IACnC,MAAM,OAAO,GAAgB,EAAE,CAAC;IAEhC,4CAA4C;IAC5C,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;QACjB,MAAM,CAAC,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;IACvC,CAAC;IAED,IAAI,IAAI,CAAC,QAAQ,EAAE,OAAO,EAAE,CAAC;QAC3B,MAAM,CAAC,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;IAChD,CAAC;IAED,iEAAiE;IACjE,IAAI,IAAI,CAAC,QAAQ,EAAE,WAAW,EAAE,CAAC;QAC/B,MAAM,CAAC,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;IACpD,CAAC;IAED,gDAAgD;IAChD,IAAI,IAAI,CAAC,SAAS;QAAE,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;IACvD,IAAI,IAAI,CAAC,SAAS;QAAE,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;IACvD,IAAI,IAAI,CAAC,SAAS;QAAE,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;IACvD,IAAI,IAAI,CAAC,OAAO;QAAE,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;IACjD,IAAI,IAAI,CAAC,QAAQ;QAAE,OAAO,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;IACpD,IAAI,IAAI,CAAC,OAAO;QAAE,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;IAEjD,mDAAmD;IACnD,IAAI,IAAI,CAAC,QAAQ,EAAE,WAAW,EAAE,CAAC;QAC/B,MAAM,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC;QAC9C,IAAI,WAAW,CAAC,UAAU;YAAE,OAAO,CAAC,SAAS,GAAG,WAAW,CAAC,UAAU,CAAC;QACvE,IAAI,WAAW,CAAC,UAAU;YAAE,OAAO,CAAC,SAAS,GAAG,WAAW,CAAC,UAAU,CAAC;QACvE,IAAI,WAAW,CAAC,QAAQ;YAAE,OAAO,CAAC,OAAO,GAAG,WAAW,CAAC,QAAQ,CAAC;QACjE,IAAI,WAAW,CAAC,QAAQ;YAAE,OAAO,CAAC,QAAQ,GAAG,WAAW,CAAC,QAAQ,CAAC;QAClE,IAAI,WAAW,CAAC,WAAW;YAAE,OAAO,CAAC,WAAW,GAAG,WAAW,CAAC,WAAW,CAAC;QAC3E,IAAI,WAAW,CAAC,WAAW;YAAE,OAAO,CAAC,UAAU,GAAG,WAAW,CAAC,WAAW,CAAC;QAC1E,IAAI,WAAW,CAAC,aAAa;YAAE,OAAO,CAAC,YAAY,GAAG,WAAW,CAAC,aAAa,CAAC;QAChF,IAAI,WAAW,CAAC,MAAM;YAAE,OAAO,CAAC,KAAK,GAAG,WAAW,CAAC,MAAM,CAAC;QAC3D,IAAI,WAAW,CAAC,QAAQ;YAAE,OAAO,CAAC,OAAO,GAAG,WAAW,CAAC,QAAQ,CAAC;QACjE,IAAI,WAAW,CAAC,MAAM;YAAE,OAAO,CAAC,MAAM,GAAG,WAAW,CAAC,MAAM,CAAC;QAC5D,IAAI,WAAW,CAAC,WAAW;YAAE,OAAO,CAAC,WAAW,GAAG,WAAW,CAAC,WAAW,CAAC;IAC7E,CAAC;IAED,OAAO,OAAO,CAAC;AACjB,CAAC;AAED;;GAEG;AACH,SAAS,gBAAgB,CAAC,IAAS;IACjC,MAAM,KAAK,GAAc,EAAE,CAAC;IAE5B,0CAA0C;IAC1C,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;QACf,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;IACnC,CAAC;IAED,IAAI,IAAI,CAAC,QAAQ,EAAE,KAAK,EAAE,CAAC;QACzB,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IAC5C,CAAC;IAED,yDAAyD;IACzD,IAAI,IAAI,CAAC,QAAQ,EAAE,OAAO,EAAE,CAAC;QAC3B,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;IAC9C,CAAC;IAED,8CAA8C;IAC9C,IAAI,IAAI,CAAC,OAAO;QAAE,KAAK,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;IAC/C,IAAI,IAAI,CAAC,SAAS;QAAE,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;IACrD,IAAI,IAAI,CAAC,KAAK;QAAE,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;IACzC,IAAI,IAAI,CAAC,QAAQ;QAAE,KAAK,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;IAClD,IAAI,IAAI,CAAC,YAAY;QAAE,KAAK,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;IAC9D,IAAI,IAAI,CAAC,YAAY;QAAE,KAAK,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;IAC9D,IAAI,IAAI,CAAC,OAAO;QAAE,KAAK,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;IAE/C,mDAAmD;IACnD,IAAI,IAAI,CAAC,QAAQ,EAAE,OAAO,EAAE,CAAC;QAC3B,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC;QACtC,IAAI,OAAO,CAAC,KAAK;YAAE,KAAK,CAAC,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC;QACjD,IAAI,OAAO,CAAC,OAAO;YAAE,KAAK,CAAC,SAAS,GAAG,OAAO,CAAC,OAAO,CAAC;QACvD,IAAI,OAAO,CAAC,cAAc;YAAE,KAAK,CAAC,cAAc,GAAG,OAAO,CAAC,cAAc,CAAC;QAC1E,IAAI,OAAO,CAAC,YAAY;YAAE,KAAK,CAAC,YAAY,GAAG,OAAO,CAAC,YAAY,CAAC;QACpE,IAAI,OAAO,CAAC,MAAM;YAAE,KAAK,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;QAClD,IAAI,OAAO,CAAC,KAAK;YAAE,KAAK,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;QAC/C,IAAI,OAAO,CAAC,YAAY;YAAE,KAAK,CAAC,YAAY,GAAG,OAAO,CAAC,YAAY,CAAC;QACpE,IAAI,OAAO,CAAC,YAAY;YAAE,KAAK,CAAC,YAAY,GAAG,OAAO,CAAC,YAAY,CAAC;QACpE,IAAI,OAAO,CAAC,UAAU;YAAE,KAAK,CAAC,QAAQ,GAAG,OAAO,CAAC,UAAU,CAAC;QAC5D,IAAI,OAAO,CAAC,UAAU;YAAE,KAAK,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC;QAC9D,IAAI,OAAO,CAAC,MAAM;YAAE,KAAK,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;QAClD,IAAI,OAAO,CAAC,cAAc;YAAE,KAAK,CAAC,cAAc,GAAG,OAAO,CAAC,cAAc,CAAC;IAC5E,CAAC;IAED,yCAAyC;IACzC,IAAI,IAAI,CAAC,QAAQ,EAAE,SAAS,EAAE,CAAC;QAC7B,KAAK,CAAC,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC;IAChD,CAAC;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAED;;;;GAIG;AACI,KAAK,UAAU,gBAAgB,CAAC,QAAgB;IACrD,IAAI,CAAC;QACH,uBAAuB;QACvB,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC7B,MAAM,IAAI,KAAK,CAAC,uBAAuB,QAAQ,EAAE,CAAC,CAAC;QACrD,CAAC;QAED,2BAA2B;QAC3B,MAAM,OAAO,GAAG,EAAE,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;QAE1C,sCAAsC;QACtC,MAAM,SAAS,GAAG,GAAG,CAAC,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QAC3C,MAAM,YAAY,GAAU,EAAE,CAAC;QAE/B,+CAA+C;QAC/C,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;YACjC,YAAY,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC;QAC5C,CAAC;QAED,oDAAoD;QACpD,mCAAmC;QACnC,MAAM,QAAQ,GAAG,YAAY,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC;QAE5E,qCAAqC;QACrC,MAAM,WAAW,GAAG,kBAAkB,CAAC,QAAQ,CAAC,CAAC;QACjD,MAAM,SAAS,GAAG,gBAAgB,CAAC,QAAQ,CAAC,CAAC;QAE7C,OAAO;YACL,OAAO,EAAE,WAAW;YACpB,KAAK,EAAE,SAAS;YAChB,OAAO,EAAE,QAAQ,CAAC,iCAAiC;SACpD,CAAC;IAEJ,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,MAAM,IAAI,KAAK,CAAC,6BAA6B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;IACzG,CAAC;AACH,CAAC;AAED;;GAEG;AACH,SAAgB,cAAc,CAAC,QAAgB;IAC7C,IAAI,CAAC;QACH,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC7B,OAAO,KAAK,CAAC;QACf,CAAC;QAED,MAAM,OAAO,GAAG,EAAE,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;QAE1C,kDAAkD;QAClD,MAAM,SAAS,GAAG,GAAG,CAAC,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QAC3C,OAAO,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;IAC9B,CAAC;IAAC,MAAM,CAAC;QACP,OAAO,KAAK,CAAC;IACf,CAAC;AACH,CAAC"}