/**
 * Utility functions for working with ION files
 */
/**
 * Quick info about an ION file
 */
declare function getIonFileInfo(filePath: string): Promise<{
    path: string;
    size: string;
    modified: string;
    isValidIon: boolean;
} | {
    parseTime: string;
    sessionId: string | undefined;
    robotName: string | undefined;
    topicCount: any;
    path: string;
    size: string;
    modified: string;
    isValidIon: boolean;
} | {
    parseError: string;
    path: string;
    size: string;
    modified: string;
    isValidIon: boolean;
}>;
/**
 * Extract specific topics from ION file
 */
declare function extractTopics(filePath: string, topicNames?: string[]): Promise<any>;
/**
 * Get session summary
 */
declare function getSessionSummary(filePath: string): Promise<{
    session: {
        id: string | undefined;
        code: string | undefined;
        type: string | undefined;
        duration: number | undefined;
        startTime: string | undefined;
        endTime: string | undefined;
        result: string | undefined;
        completeness: any;
        operator: string | undefined;
        mapName: string | undefined;
    };
    robot: {
        id: string | undefined;
        name: string | undefined;
        enterprise: string | undefined;
        firmware: string | undefined;
        lidarModel: any;
    };
}>;
export { getIonFileInfo, extractTopics, getSessionSummary };
//# sourceMappingURL=ionUtils.d.ts.map