"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const path_1 = __importDefault(require("path"));
const topicReader_1 = require("./topicReader");
const ionParser_1 = require("./ionParser");
const app = (0, express_1.default)();
const PORT = process.env.PORT || 3001;
// Middleware
app.use((0, cors_1.default)());
app.use(express_1.default.json());
// Default ION file path (can be configured via environment variable)
const ION_FILE_PATH = process.env.ION_FILE_PATH || path_1.default.join(__dirname, '../../OhmniClean_log.ion');
/**
 * Health check endpoint
 */
app.get('/health', (req, res) => {
    res.json({ status: 'ok', timestamp: new Date().toISOString() });
});
/**
 * Get ION file metadata
 */
app.get('/api/metadata', async (req, res) => {
    try {
        const metadata = await (0, ionParser_1.parseIonMetadata)(ION_FILE_PATH);
        res.json({
            session: metadata.session,
            robot: metadata.robot
        });
    }
    catch (error) {
        console.error('Error getting metadata:', error);
        res.status(500).json({
            error: 'Failed to get metadata',
            message: error instanceof Error ? error.message : String(error)
        });
    }
});
/**
 * Get list of readable topics
 */
app.get('/api/topics/readable', async (req, res) => {
    try {
        const topics = await (0, topicReader_1.getReadableTopics)(ION_FILE_PATH);
        res.json(topics);
    }
    catch (error) {
        console.error('Error getting readable topics:', error);
        res.status(500).json({
            error: 'Failed to get readable topics',
            message: error instanceof Error ? error.message : String(error)
        });
    }
});
/**
 * Get playback time range
 */
app.get('/api/playback/timerange', async (req, res) => {
    try {
        const timeRange = await (0, topicReader_1.getPlaybackTimeRange)(ION_FILE_PATH);
        res.json(timeRange);
    }
    catch (error) {
        console.error('Error getting time range:', error);
        res.status(500).json({
            error: 'Failed to get time range',
            message: error instanceof Error ? error.message : String(error)
        });
    }
});
/**
 * Get message at specific timestamp for a topic
 */
app.get('/api/topics/:topicName/message', async (req, res) => {
    try {
        const { topicName } = req.params;
        const { timestamp } = req.query;
        if (!timestamp) {
            return res.status(400).json({ error: 'timestamp parameter is required' });
        }
        const timestampNum = parseFloat(timestamp);
        if (isNaN(timestampNum)) {
            return res.status(400).json({ error: 'timestamp must be a valid number' });
        }
        const message = await (0, topicReader_1.getMessageAtTimestamp)(ION_FILE_PATH, topicName, timestampNum);
        if (!message) {
            return res.status(404).json({ error: 'No message found for the given timestamp' });
        }
        res.json(message);
    }
    catch (error) {
        console.error('Error getting message:', error);
        res.status(500).json({
            error: 'Failed to get message',
            message: error instanceof Error ? error.message : String(error)
        });
    }
});
/**
 * Get all messages for a topic (optionally within time range)
 */
app.get('/api/topics/:topicName/messages', async (req, res) => {
    try {
        const { topicName } = req.params;
        const { startTime, endTime, limit } = req.query;
        let startTimeNum;
        let endTimeNum;
        if (startTime) {
            startTimeNum = parseFloat(startTime);
            if (isNaN(startTimeNum)) {
                return res.status(400).json({ error: 'startTime must be a valid number' });
            }
        }
        if (endTime) {
            endTimeNum = parseFloat(endTime);
            if (isNaN(endTimeNum)) {
                return res.status(400).json({ error: 'endTime must be a valid number' });
            }
        }
        let messages = await (0, topicReader_1.getTopicMessages)(ION_FILE_PATH, topicName, startTimeNum, endTimeNum);
        // Apply limit if specified
        if (limit) {
            const limitNum = parseInt(limit);
            if (!isNaN(limitNum) && limitNum > 0) {
                messages = messages.slice(0, limitNum);
            }
        }
        res.json({
            topicName,
            messageCount: messages.length,
            messages
        });
    }
    catch (error) {
        console.error('Error getting messages:', error);
        res.status(500).json({
            error: 'Failed to get messages',
            message: error instanceof Error ? error.message : String(error)
        });
    }
});
/**
 * Get topic details
 */
app.get('/api/topics/:topicName/details', async (req, res) => {
    try {
        const { topicName } = req.params;
        const topics = await (0, topicReader_1.getReadableTopics)(ION_FILE_PATH);
        const topic = topics.find(t => t.name === topicName);
        if (!topic) {
            return res.status(404).json({ error: 'Topic not found' });
        }
        res.json(topic);
    }
    catch (error) {
        console.error('Error getting topic details:', error);
        res.status(500).json({
            error: 'Failed to get topic details',
            message: error instanceof Error ? error.message : String(error)
        });
    }
});
/**
 * Error handling middleware
 */
app.use((err, req, res, next) => {
    console.error('Unhandled error:', err);
    res.status(500).json({
        error: 'Internal server error',
        message: err.message
    });
});
/**
 * 404 handler
 */
app.use((req, res) => {
    res.status(404).json({ error: 'Endpoint not found' });
});
/**
 * Start server
 */
app.listen(PORT, () => {
    console.log(`🚀 ION Playback Server running on port ${PORT}`);
    console.log(`📁 Using ION file: ${ION_FILE_PATH}`);
    console.log(`🌐 API endpoints:`);
    console.log(`   GET /health`);
    console.log(`   GET /api/metadata`);
    console.log(`   GET /api/topics/readable`);
    console.log(`   GET /api/playback/timerange`);
    console.log(`   GET /api/topics/:topicName/message?timestamp=123`);
    console.log(`   GET /api/topics/:topicName/messages?startTime=123&endTime=456&limit=100`);
    console.log(`   GET /api/topics/:topicName/details`);
});
exports.default = app;
//# sourceMappingURL=server.js.map