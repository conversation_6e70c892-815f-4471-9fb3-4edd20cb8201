/**
 * Interface for readable topic information
 */
export interface ReadableTopic {
    name: string;
    type: string;
    frequency: number;
    messageCount: number;
    firstTimestamp: number;
    lastTimestamp: number;
}
/**
 * Interface for topic message
 */
export interface TopicMessage {
    timestamp: number;
    data: any;
    sequenceNumber?: number;
}
/**
 * Interface for playback time range
 */
export interface PlaybackTimeRange {
    start: number;
    end: number;
    duration: number;
}
/**
 * Get list of readable topics from ION file
 */
export declare function getReadableTopics(filePath: string): Promise<ReadableTopic[]>;
/**
 * Get message at specific timestamp using nearest-timestamp matching
 */
export declare function getMessageAtTimestamp(filePath: string, topicName: string, timestamp: number): Promise<TopicMessage | null>;
/**
 * Get playback time range for the entire session
 */
export declare function getPlaybackTimeRange(filePath: string): Promise<PlaybackTimeRange>;
/**
 * Get all messages for a topic within a time range
 */
export declare function getTopicMessages(filePath: string, topicName: string, startTime?: number, endTime?: number): Promise<TopicMessage[]>;
//# sourceMappingURL=topicReader.d.ts.map